# Dockerfile simplificado para Windows
# Usa JAR pre-construido

FROM eclipse-temurin:17-jre

# Set working directory
WORKDIR /app

# Copy the JAR file (assumes it's already built)
COPY target/educagestor-api-1.0.0.jar app.jar

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/api/actuator/health || exit 1

# Run the application
ENTRYPOINT ["java", "-jar", "app.jar"]
