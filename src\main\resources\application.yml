server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: educagestor-api
  # Datasource configuration is now profile-specific (see below)

  jpa:
    hibernate:
      ddl-auto: update # Options: create, create-drop, update, validate, none
    show-sql: true # Set to true for development to see generated SQL
    properties:
      hibernate:
        format_sql: true
    defer-datasource-initialization: true

  sql:
    init: # For schema.sql and data.sql if present for a profile
      mode: always # Options: always, embedded, never
  security:
    user:
      name: admin
      password: ${ADMIN_PASSWORD:admin}
      roles: ADMIN

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:mySecretKey123456789012345678901234567890}
  expiration: 86400000 # 24 hours
  refresh-expiration: 604800000 # 7 days

# Logging Configuration
logging:
  level:
    com.educagestor: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/educagestor-api.log

# Swagger/OpenAPI Configuration
springdoc:
  api-docs:
    path: /api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
    tagsSorter: alpha
    enabled: true
  show-actuator: true

# CORS Configuration
cors:
  allowed-origins:
    - http://localhost:3000
    - http://localhost:3001
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers: "*"
  allow-credentials: true

# Pagination defaults
pagination:
  default-page-size: 20
  max-page-size: 100

---
spring:
  config:
    activate:
      on-profile: mysql
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:educagestor_db}?useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true&createDatabaseIfNotExist=true
    username: ${DB_USERNAME:educagestor_user1}
    password: ${DB_PASSWORD:educagestor_pass}
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    database-platform: org.hibernate.dialect.MySQLDialect # Use MySQL8Dialect for MySQL 8+
---
spring:
  config:
    activate:
      on-profile: sqlserver
  datasource:
    url: jdbc:sqlserver://${DB_HOST:localhost}:${DB_PORT:1433};databaseName=${DB_NAME:educagestor_db};encrypt=false;trustServerCertificate=true
    # For production with SQL Server, encrypt=true is recommended. trustServerCertificate=true is for dev convenience.
    username: ${DB_USERNAME:educagestor_user}
    password: ${DB_PASSWORD:educagestor_pass}
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
  jpa:
    database-platform: org.hibernate.dialect.SQLServerDialect # Or SQLServer2012Dialect etc. for specific versions
