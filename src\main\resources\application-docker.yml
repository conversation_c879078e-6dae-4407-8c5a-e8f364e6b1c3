server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: educagestor-api
  # The active profile (e.g., mysql or sqlserver) will determine the datasource properties.
  # These properties (url, username, password, driver, dialect) are defined in application.yml per profile.
  # Environment variables DB_HOST, DB_PORT, DB_NAME, DB_USERNAME, DB_PASSWORD
  # will be used by the respective profile configuration.
  jpa:
    show-sql: false
    properties:
      hibernate:
        # Dialect will be set by the active profile (mysql or sqlserver)
        format_sql: false # Typically false for production/docker
    # ddl-auto and defer-datasource-initialization will be inherited from the active profile in application.yml

# sql.init.mode will be inherited from the active profile in application.yml

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:mySecretKey123456789012345678901234567890} # Ensure this is set as an environment variable
  expiration: 86400000 # 24 hours
  refresh-expiration: 604800000 # 7 days

# Logging Configuration
logging:
  level:
    com.educagestor: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/educagestor-api.log

# Swagger Configuration
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
