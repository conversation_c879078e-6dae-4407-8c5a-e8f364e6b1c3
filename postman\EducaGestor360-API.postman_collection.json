{"info": {"_postman_id": "12345678-1234-1234-1234-**********12", "name": "EducaGestor360 API", "description": "Comprehensive Educational Management Platform API\n\nThis collection contains all the endpoints for the EducaGestor360 API including authentication, user management, student management, teacher management, course management, enrollment management, and grade management.\n\n## Authentication\nMost endpoints require JWT authentication. First login or register to get a JWT token, then use it in the Authorization header for subsequent requests.\n\n## Base URL\nLocal Development: http://localhost:8080/api", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"admin\",\n    \"password\": \"admin123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}, "description": "Authenticate user and get JWT tokens"}}, {"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"newuser\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"firstName\": \"New\",\n    \"lastName\": \"User\",\n    \"phoneNumber\": \"+**********\",\n    \"roles\": [\"STUDENT\"]\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}, "description": "Register a new user account"}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/refresh", "host": ["{{baseUrl}}"], "path": ["auth", "refresh"]}, "description": "Refresh JWT access token"}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}, "description": "Logout user (client-side token invalidation)"}}], "description": "Authentication endpoints for login, registration, and token management"}, {"name": "User Management", "item": [{"name": "Get Current User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/users/profile", "host": ["{{baseUrl}}"], "path": ["users", "profile"]}, "description": "Get the current authenticated user's profile"}}, {"name": "Update Current User Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"firstName\": \"Updated\",\n    \"lastName\": \"Name\",\n    \"phoneNumber\": \"+1234567899\"\n}"}, "url": {"raw": "{{baseUrl}}/users/profile", "host": ["{{baseUrl}}"], "path": ["users", "profile"]}, "description": "Update the current authenticated user's profile"}}, {"name": "Get All Users (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/users?page=0&size=20&sortBy=username&sortDir=asc", "host": ["{{baseUrl}}"], "path": ["users"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "20"}, {"key": "sortBy", "value": "username"}, {"key": "sortDir", "value": "asc"}]}, "description": "Get all users with pagination (Admin only)"}}], "description": "User profile management endpoints"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Auto-save JWT tokens from login/register responses", "if (pm.response.json() && pm.response.json().token) {", "    pm.environment.set('accessToken', pm.response.json().token);", "}", "if (pm.response.json() && pm.response.json().refreshToken) {", "    pm.environment.set('refreshToken', pm.response.json().refreshToken);", "}"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080/api", "type": "string"}]}