version: "3.8"

services:
  # EducaGestor360 API
  educagestor-api:
    build:
      context: .
    container_name: educagestor-api
    environment:
      SPRING_PROFILES_ACTIVE: docker,mysql # Default to mysql for docker, can be changed
      # For connecting to <PERSON> on host from Docker: use localhost with network_mode: host
      DB_HOST: localhost # Using localhost with host network mode
      DB_PORT: 3306 # Default MySQL port
      DB_NAME: educagestor_db # Or your chosen DB name
      DB_USERNAME: educagestor_user1 # Or your chosen DB user
      DB_PASSWORD: educagestor_pass # Or your chosen DB password
      JWT_SECRET: mySecretKey123456789012345678901234567890
    ports:
      - "8080:8080"
    network_mode: host # Use host network to access localhost MySQL directly
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # pgAdmin is removed as it's PostgreSQL specific.
  # pgAdmin (Optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: educagestor-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    ports:
      - "5050:80"
    restart: unless-stopped
    profiles:
      - tools
    # Note: pgAdmin is for PostgreSQL. For MySQL, use MySQL Workbench or similar.
    # For SQL Server, use SQL Server Management Studio (SSMS) or Azure Data Studio.

volumes:
  pgadmin_data:
    driver: local

networks:
  educagestor-network:
    driver: bridge
