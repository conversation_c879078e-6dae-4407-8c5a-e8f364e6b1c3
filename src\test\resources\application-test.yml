server:
  port: 0

spring:
  application:
    name: educagestor-api-test
    
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
    defer-datasource-initialization: true
    
  sql:
    init:
      mode: never
      
  h2:
    console:
      enabled: true

# JWT Configuration for testing
jwt:
  secret: testSecretKey123456789012345678901234567890
  expiration: 86400000 # 24 hours
  refresh-expiration: 604800000 # 7 days

# Logging Configuration for tests
logging:
  level:
    com.educagestor: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Disable Swagger for tests
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
