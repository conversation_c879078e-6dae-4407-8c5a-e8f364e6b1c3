spring:
  datasource:
    url: jdbc:h2:mem:educagestor_db;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
    defer-datasource-initialization: true
    
  sql:
    init:
      mode: never # Disable data.sql loading for H2
      
  h2:
    console:
      enabled: true
      path: /h2-console

# Swagger/OpenAPI Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
    tagsSorter: alpha
    enabled: true
  show-actuator: true

# Logging Configuration for H2 development
logging:
  level:
    com.educagestor: DEBUG
    org.springframework.security: INFO
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
